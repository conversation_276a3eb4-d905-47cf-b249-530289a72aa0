import React, { useEffect, useMemo } from 'react';
import { Layout as AntLayout, Button, Avatar, Dropdown } from 'antd';
import { Outlet, useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuthStore } from '@/store/authStore';
import Header from '@/components/Header';

const { Content, Footer, Sider, Header: AntHeader } = AntLayout;

const Layout: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { user, isAuthenticated, permissions } = useAuthStore();
  // console.log('isAuthenticated----', isAuthenticated); // false
  // console.log('permissions----', permissions); // []
  // console.log('user----', user); // 和后台返回数据一致的user对象

  // 初始化权限
  useEffect(() => {
    useAuthStore.getState().initializeAuth();
  }, []);
  // 左侧菜单
  const leftMenuItems = useMemo(
    () =>
      permissions.map((item, index) => {
        return (
          <div
            key={item.code || index}
            className="cursor-pointer rounded p-2 hover:bg-gray-100"
            onClick={() => navigate(item.url)}
          >
            {item.name}
          </div>
        );
      }),
    [permissions, navigate]
  );
  const siderStyle: React.CSSProperties = {
    textAlign: 'center',
    lineHeight: '120px',
    color: 'white',
    // backgroundColor: 'white',
  };

  return (
    <AntLayout className="min-h-screen bg-page-bg">
      <AntHeader>
        <Header fixed={false} />
      </AntHeader>
      <AntLayout>
        <Sider width="25%" style={siderStyle}>
          {leftMenuItems}
        </Sider>
        <Content className="flex-1">
          <Outlet />
        </Content>
      </AntLayout>
    </AntLayout>
  );
  // return (
  //   <div>
  //     <Header />
  //     <div className="min-h-screen text-label pt-120px"></div>
  //   </div>
  // );
};

export default Layout;
