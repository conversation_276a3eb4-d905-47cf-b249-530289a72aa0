import React from 'react';
import { Dropdown, Button, Space } from 'antd';
import { useAppStore } from '@/store';
import { GlobalOutlined, DownOutlined } from '@ant-design/icons';
import { SUPPORTED_LANGUAGES } from '../locales';
import type { Language } from '@/store/index';

const SelectLanguage: React.FC<{ className?: string }> = ({ className }) => {
  const { language, setLanguage } = useAppStore();

  const handleLanguageChange = ({ key }: { key: string }) => {
    setLanguage(key as Language);
  };

  // 获取当前选中语言的显示名称
  const currentLanguage = SUPPORTED_LANGUAGES.find(
    lang => lang.code === language
  );

  const menuItems = SUPPORTED_LANGUAGES.map(lang => ({
    key: lang.code,
    label: (
      <div className="flex items-center">
        <span>{lang.name}</span>
      </div>
    ),
  }));

  return (
    <div className={`${className}`}>
      <Dropdown
        menu={{
          items: menuItems,
          onClick: handleLanguageChange,
        }}
        getPopupContainer={() => document.body}
      >
        <a className="cursor-pointer" onClick={e => e.preventDefault()}>
          <Space style={{ color: 'var(--color-label)' }}>
            {currentLanguage?.name}
            <DownOutlined />
          </Space>
        </a>
      </Dropdown>
    </div>
  );
};

export default SelectLanguage;
