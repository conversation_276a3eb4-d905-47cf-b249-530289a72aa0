import React from 'react';
import { Dropdown, Button, Space } from 'antd';
import { useAppStore } from '@/store';
import { GlobalOutlined, DownOutlined } from '@ant-design/icons';
import { SUPPORTED_LANGUAGES } from '../locales';
import type { Language } from '@/store/index';
import { ConfigProvider } from 'antd';

const SelectLanguage: React.FC<{ className?: string }> = ({ className }) => {
  const { language, setLanguage } = useAppStore();

  const handleLanguageChange = ({ key }: { key: string }) => {
    setLanguage(key as Language);
  };

  // 获取当前选中语言的显示名称
  const currentLanguage = SUPPORTED_LANGUAGES.find(
    lang => lang.code === language
  );

  const menuItems = SUPPORTED_LANGUAGES.map(lang => ({
    key: lang.code,
    label: (
      <div className="flex items-center">
        <span>{lang.name}</span>
      </div>
    ),
  }));

  return (
    <ConfigProvider
      theme={{
        components: {
          Dropdown: {
            colorPrimary: 'var(--color-primary)',
            controlItemBgHover: 'var(--color-primary)',
            controlItemBgActiveHover: 'var(--color-primary)',
            colorText: 'var(--color-label)',
            colorBgElevated: 'rgba(0,0,0,0.5)',
            fontSize: 16,
          },
        },
      }}
    >
      <div className={`${className}`}>
        <Dropdown
          menu={{
            items: menuItems,
            onClick: handleLanguageChange,
          }}
        >
          <a className="cursor-pointer" onClick={e => e.preventDefault()}>
            <Space>
              {currentLanguage?.name}
              <DownOutlined />
            </Space>
          </a>
        </Dropdown>
      </div>
    </ConfigProvider>
  );
};

export default SelectLanguage;
