import { useState, useEffect } from 'react';
import { useAppStore } from '../store';
import { ApiService } from '../services';
import { loadingManager, type LoadingState } from '../services/request';

/**
 * 简单的API数据Hook - 自动处理语言切换
 * @param url API地址
 * @param params 其他参数
 */
export const useApiData = (url: string, params: any = {}) => {
  const { language } = useAppStore();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    ApiService.get(url, {
      params: { ...params, lang: language },
    })
      .then(res => setData(res.data))
      .catch(err => console.error(err))
      .finally(() => setLoading(false));
  }, [url, language, JSON.stringify(params)]);

  return { data, loading };
};

/**
 * 用于订阅全局loading状态的hook
 */
export const useLoading = () => {
  const [loadingState, setLoadingState] = useState<LoadingState>(() =>
    loadingManager.getState(),
  );

  useEffect(() => {
    const unsubscribe = loadingManager.subscribe(setLoadingState);
    return unsubscribe;
  }, []);

  return loadingState;
};
