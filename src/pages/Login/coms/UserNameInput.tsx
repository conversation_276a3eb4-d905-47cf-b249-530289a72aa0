import React, { useEffect } from 'react';
import { Form, Input, Button } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import type { FormInstance, Rule } from 'antd/es/form';
import type { LoginFormData } from '@/types';
import FormActions from './FormActions';

interface UserNameInputProps {
  form: FormInstance<LoginFormData>;
  onSubmit: (values: { email: string }) => void;
  initialEmail?: string; // 添加初始邮箱值支持
}

const UserNameInput: React.FC<UserNameInputProps> = ({
  form,
  onSubmit,
  initialEmail = '',
}) => {
  const { t } = useLanguage();

  // 当initialEmail变化时，设置表单初始值
  useEffect(() => {
    if (initialEmail) {
      form.setFieldsValue({ email: initialEmail });
    }
  }, [initialEmail, form]);

  const handleSubmit = () => {
    form
      .validateFields(['email'])
      .then(values => {
        onSubmit(values);
      })
      .catch(errorInfo => {
        console.log('Validation failed:', errorInfo);
      });
  };

  const rules: Rule[] = [
    {
      required: true,
    },
    {
      type: 'email',
      message: t('common.form.emailInvalid'),
    },
  ];

  return (
    <>
      <Form.Item
        name="email"
        rules={rules}
        messageVariables={{ label: t('auth.register.step1.form.email') }}
        label={
          <span className="leading-10">
            {t('auth.register.step1.form.email')}
          </span>
        }
        className="mb-6"
      >
        <Input
          placeholder={t('common.form.emailRequired')}
          className="placeholder:font-inter h-[54px] rounded-md border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25)"
          size="large"
        />
      </Form.Item>
      <FormActions onSubmit={handleSubmit} />

      <div className="h-10 text-center leading-10"></div>
    </>
  );
};

export default UserNameInput;
