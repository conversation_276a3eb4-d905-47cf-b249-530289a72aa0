import { ApiService, getUrl } from './request';
import type { LoginFormData } from '@/types';
import type { RegisterFormData } from '@/types';

// 用户相关API
export const user = {
  register: (params: RegisterFormData) => {
    return ApiService.post(getUrl('mock', '/api/auth/register'), params);
  },
  login: (params: LoginFormData) => {
    return ApiService.post(getUrl('mock', '/api/auth/login'), {
      username: params.email,
      password: params.password,
    });
  },
  getUserInfo: () => ApiService.get(getUrl('mock', '/api/user/me')),
};
